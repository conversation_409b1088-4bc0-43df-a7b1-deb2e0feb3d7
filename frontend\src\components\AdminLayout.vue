<template>
  <div class="admin-layout">
    <!-- 左侧导航栏 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <h2 class="logo">管理后台</h2>
      </div>

      <nav class="sidebar-nav">
        <router-link
          to="/admin/dashboard"
          class="nav-item"
          :class="{ active: $route.path === '/admin/dashboard' }"
        >
          <span class="nav-icon">📊</span>
          <span class="nav-text">微信号管理</span>
        </router-link>

        <router-link
          to="/admin/users"
          class="nav-item"
          :class="{ active: $route.path === '/admin/users' }"
        >
          <span class="nav-icon">👥</span>
          <span class="nav-text">用户管理</span>
        </router-link>

        <router-link
          to="/admin/keys"
          class="nav-item"
          :class="{ active: $route.path === '/admin/keys' }"
        >
          <span class="nav-icon">🔑</span>
          <span class="nav-text">密钥管理</span>
        </router-link>

        <router-link
          to="/admin/medicine"
          class="nav-item"
          :class="{ active: $route.path === '/admin/medicine' }"
        >
          <span class="nav-icon">🌿</span>
          <span class="nav-text">中药药方管理</span>
        </router-link>
      </nav>

      <div class="sidebar-footer">
        <div class="user-info">
          <div class="user-name">{{ currentAdmin?.name || "管理员" }}</div>
          <div class="user-role">{{ getRoleText(currentAdmin?.role) }}</div>
        </div>
        <button @click="logout" class="logout-btn">
          <span class="logout-icon">🚪</span>
          <span class="logout-text">退出登录</span>
        </button>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <div class="content-header">
        <h1 class="page-title">{{ getPageTitle() }}</h1>
      </div>

      <div class="content-body">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AdminLayout",
  data() {
    return {
      currentAdmin: null,
    };
  },
  mounted() {
    // 从localStorage获取当前管理员信息
    const adminInfo = localStorage.getItem("currentAdmin");
    if (adminInfo) {
      this.currentAdmin = JSON.parse(adminInfo);
    }
  },
  methods: {
    getRoleText(role) {
      const roleMap = {
        super_admin: "超级管理员",
        admin: "管理员",
        user: "普通用户",
      };
      return roleMap[role] || "未知角色";
    },

    getPageTitle() {
      const titleMap = {
        "/admin/dashboard": "微信号管理",
        "/admin/users": "用户管理",
        "/admin/keys": "密钥管理",
        "/admin/medicine": "中药药方管理",
      };
      return titleMap[this.$route.path] || "管理后台";
    },

    logout() {
      localStorage.removeItem("adminLoggedIn");
      localStorage.removeItem("currentAdmin");
      this.$router.push("/admin");
    },
  },
};
</script>

<style scoped>
.admin-layout {
  display: flex;
  min-height: 100vh;
  background-image: url("../assets/bg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.sidebar {
  width: 280px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  padding: 30px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.logo {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin: 0;
  text-align: center;
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #666;
  text-decoration: none;
  transition: all 0.3s;
  border-left: 3px solid transparent;
}

.nav-item:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.nav-item.active {
  background: rgba(102, 126, 234, 0.15);
  color: #667eea;
  border-left-color: #667eea;
  font-weight: 600;
}

.nav-icon {
  font-size: 20px;
  margin-right: 12px;
  width: 24px;
  text-align: center;
}

.nav-text {
  font-size: 16px;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.user-info {
  margin-bottom: 15px;
  text-align: center;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.user-role {
  font-size: 14px;
  color: #666;
}

.logout-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.logout-btn:hover {
  background: #ff3838;
}

.logout-icon {
  margin-right: 8px;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 20px 30px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.content-body {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .admin-layout {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
  }

  .sidebar-nav {
    display: flex;
    overflow-x: auto;
    padding: 10px 0;
  }

  .nav-item {
    flex-direction: column;
    min-width: 80px;
    text-align: center;
    padding: 10px;
    border-left: none;
    border-bottom: 3px solid transparent;
  }

  .nav-item.active {
    border-left: none;
    border-bottom-color: #667eea;
  }

  .nav-icon {
    margin-right: 0;
    margin-bottom: 4px;
  }

  .nav-text {
    font-size: 12px;
  }

  .sidebar-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .user-info {
    margin-bottom: 0;
    text-align: left;
  }

  .logout-btn {
    width: auto;
    padding: 8px 16px;
  }

  .content-header {
    padding: 15px 20px;
  }

  .page-title {
    font-size: 24px;
  }

  .content-body {
    padding: 20px;
  }
}
</style>
